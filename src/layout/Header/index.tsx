import ThemeToggle from '@components/ThemeToggle';
import { Hidden, Link, Toolbar, Typography, Box } from '@mui/material';
import Routes from '@routes';
import NextLink from 'next/link';
import { FC } from 'react';
import DesktopHeader from './DesktopHeader';
import { DefaultAppBar } from './styles';

const Header: FC = () => {
  return (
    <Box component="header">
      <DefaultAppBar position="relative" color="primary">
        <Toolbar>
          <NextLink href={Routes.Home} passHref>
            <Typography variant="h6" component={Link} color="white" sx={{ fontWeight: 'bold' }}>
              Pokecardmaker.org
            </Typography>
          </NextLink>
          <ThemeToggle />
          <Hidden smDown>
            <DesktopHeader />
          </Hidden>
        </Toolbar>
      </DefaultAppBar>
      {/* <Hidden mdUp>
      <MobileHeader />
    </Hidden> */}
    </Box>
  );
};

export default Header;
