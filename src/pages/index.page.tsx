import { FC } from 'react';
import { Typography, Box, Container } from '@mui/material';
import CardOptionsForm from '@cardEditor/cardOptions/components/CardOptionsForm';
import CardDisplay from '@cardEditor/cardStyles/components/CardDisplay';
import { SEO } from '@layout';
import CardDownloader from '@cardEditor/cardOptions/components/atoms/CardDownloader';
import StructuredData from '@components/StructuredData';
import SEOContent from '@components/SEOContent';
import FAQ from '@components/FAQ';
import { siteDescription } from 'src/constants';
import { CardWrapper, Wrapper } from './styles';

const Home: FC = () => (
  <>
    <SEO
      fullTitle="Pokemon Card Maker - Create Custom Pokemon Cards Online | Pokecardmaker.org"
      description="Create stunning custom Pokemon cards with our free online Pokemon card maker. Design Sword & Shield, Scarlet & Violet cards with professional templates. Easy to use!"
    />
    <StructuredData type="WebApplication" />

    {/* Hero Section with H1 */}
    <Box component="section" sx={{ textAlign: 'center', mb: 4 }}>
      <Typography
        variant="h1"
        component="h1"
        sx={{
          fontSize: { xs: '2rem', md: '2.5rem' },
          fontWeight: 'bold',
          mb: 2,
          color: 'primary.main'
        }}
      >
        Pokemon Card Maker - Create Custom Pokemon Cards
      </Typography>
      <Typography
        variant="h2"
        component="h2"
        sx={{
          fontSize: { xs: '1.2rem', md: '1.5rem' },
          fontWeight: 'normal',
          mb: 3,
          color: 'text.secondary'
        }}
      >
        Design professional Pokemon cards online with our free card creator tool
      </Typography>
    </Box>

    {/* Main Content Area */}
    <Box component="main" role="main">
      <Wrapper>
        <Box component="section" aria-label="Card customization options">
          <CardOptionsForm />
        </Box>
        <CardWrapper>
          <Box component="section" aria-label="Card preview and download">
            <CardDisplay />
            <CardDownloader />
          </Box>
        </CardWrapper>
      </Wrapper>

      {/* SEO Content Section */}
      <SEOContent />

      {/* FAQ Section */}
      <FAQ />
    </Box>
  </>
);

export default Home;
